rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // === 사용자 기본 문서 ===
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // 전화번호 업데이트 시 추가 검증
      allow update: if request.auth != null
        && request.auth.uid == userId
        && (
          // 전화번호가 변경되지 않거나
          !('phone' in request.resource.data) ||
          resource.data.phone == request.resource.data.phone ||
          // 새로운 전화번호인 경우 (중복 체크는 Functions에서 처리)
          request.resource.data.phone != null
        );

      // === 사용자별 행사 데이터 (클라이언트 권한 관리 지원) ===
      match /events/{eventId} {
        // 인증된 사용자만 접근 가능 (소유자 + 초대받은 사용자)
        // 실제 권한 검증은 클라이언트에서 수행
        allow read, write: if request.auth != null;

        // === 행사별 상품 데이터 ===
        match /products/{productId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 판매자 데이터 ===
        match /sellers/{sellerId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 판매 기록 ===
        match /sales_logs/{logId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 선입금 데이터 ===
        match /prepayments/{prepaymentId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 선입금 가상 상품 ===
        match /prepayment_virtual_products/{virtualProductId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 선입금-상품 연결 ===
        match /prepayment_product_links/{linkId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 카테고리 데이터 ===
        match /categories/{categoryId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 수익 목표 ===
        match /revenue_goals/{goalId} {
          allow read, write: if request.auth != null;
        }

        // === 행사별 체크리스트 템플릿 ===
        match /checklist_templates/{templateId} {
          allow read, write: if request.auth != null;

          // 체크리스트 아이템
          match /items/{itemId} {
            allow read, write: if request.auth != null;
          }
        }

        // === 행사별 세트 할인 ===
        match /set_discounts/{discountId} {
          allow read, write: if request.auth != null;
        }
      }

      // === 사용자 설정 ===
      match /settings/{settingId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // === 구독 정보 ===
      match /subscriptions/{subscriptionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // === SMS 인증 문서 ===
    match /sms_verifications/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // === 행사 권한 관리 (초대 시스템용) ===
    match /event_permissions/{eventId} {
      // 행사 권한 컬렉션 자체
      allow read: if request.auth != null;

      match /users/{userId} {
        // 본인의 권한 정보만 읽기/쓰기 가능
        allow read, write: if request.auth != null && request.auth.uid == userId;
        // 권한 부여는 행사 소유자만 가능 (클라이언트에서 검증)
        allow create: if request.auth != null;
      }
    }

    // === 행사 초대 관리 ===
    match /event_invitations/{invitationId} {
      // 초대 코드로 조회 가능 (인증된 사용자만)
      allow read: if request.auth != null;
      // 초대 생성/수정은 소유자만 가능 (클라이언트에서 검증)
      allow write: if request.auth != null;
    }

    // === 관리자 전용 문서들 ===
    match /admin/{document} {
      allow read, write: if request.auth != null &&
        request.auth.token.email in ['<EMAIL>'];

      // 관리자 문서의 하위 컬렉션들
      match /{subcollection=**} {
        allow read, write: if request.auth != null &&
          request.auth.token.email in ['<EMAIL>'];
      }
    }

    // === 공개 읽기 전용 데이터 (앱 업데이트 정보 등) ===
    match /public/{document} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.token.email in ['<EMAIL>'];
    }

    // === 사용량 통계 (Functions에서 관리) ===
    match /usage_stats/{document} {
      allow read: if request.auth != null &&
        request.auth.token.email in ['<EMAIL>'];
      allow write: if false; // Functions에서만 쓰기 가능
    }

    // === 기본 거부 규칙 ===
    // 위에서 명시적으로 허용되지 않은 모든 접근은 거부
    match /{document=**} {
      allow read, write: if false;
    }
  }
}