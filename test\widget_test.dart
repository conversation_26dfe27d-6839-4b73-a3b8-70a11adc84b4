// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:parabara/utils/app_check_config.dart';

void main() {
  group('바라 부스 매니저 기본 테스트', () {
    testWidgets('앱 기본 구조 테스트', (WidgetTester tester) async {
      // 간단한 MaterialApp만 테스트
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(home: Scaffold(body: Text('바라 부스 매니저'))),
        ),
      );

      // 기본 구조 확인
      expect(find.byType(ProviderScope), findsOneWidget);
      expect(find.byType(MaterialApp), findsOneWidget);
      expect(find.text('바라 부스 매니저'), findsOneWidget);
    });

    test('App Check 설정 테스트', () {
      // App Check 설정 정보 확인
      final configInfo = AppCheckConfig.configInfo;

      expect(configInfo['platform'], isNotNull);
      expect(configInfo['androidProvider'], isNotNull);
      expect(configInfo['appleProvider'], isNotNull);
      expect(configInfo['webSupport'], isNotNull);
    });

    test('reCAPTCHA v2 사이트 키 확인 (전화번호 인증용)', () {
      final siteKey = AppCheckConfig.webRecaptchaV2SiteKey;
      expect(siteKey, isNotEmpty);
      expect(siteKey.startsWith('6L'), isTrue);
      expect(siteKey, equals('6LdfCK4rAAAAAFjdN1h44yAUhSj4aLeklB3U1DO_')); // 기존 v2 키
    });

    test('reCAPTCHA v3 사이트 키 확인 (백그라운드용)', () {
      final siteKey = AppCheckConfig.webRecaptchaV3SiteKey;
      expect(siteKey, isNotEmpty);
      expect(siteKey.startsWith('6L'), isTrue);
      expect(siteKey, equals('6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S')); // 새로운 v3 키
    });

    test('reCAPTCHA v3 시크릿 키 확인', () {
      final secretKey = AppCheckConfig.webRecaptchaV3SecretKey;
      expect(secretKey, isNotEmpty);
      expect(secretKey.startsWith('6L'), isTrue);
      expect(secretKey, equals('6LcfOK8rAAAAAE30usCP1onSES4kj2fZ7xSXSfEz')); // v3 시크릿 키
    });

    test('하위 호환성 확인', () {
      // 기본 webRecaptchaSiteKey는 v2 키를 반환해야 함
      expect(AppCheckConfig.webRecaptchaSiteKey, equals(AppCheckConfig.webRecaptchaV2SiteKey));
    });
  });
}
