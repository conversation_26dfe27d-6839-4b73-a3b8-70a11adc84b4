<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>전화번호 인증 - 바라 부스 매니저</title>
    <!-- Firebase SDK -->
    <script type="module">
        // Firebase 설정
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { initializeAppCheck, ReCaptchaV3Provider } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app-check.js";

        const firebaseConfig = {
            apiKey: "AIzaSyCqCc7aTMTXAGJfpb7fYe713EuTbGKEzMI",
            authDomain: "parabara-1a504.firebaseapp.com",
            projectId: "parabara-1a504",
            storageBucket: "parabara-1a504.firebasestorage.app",
            messagingSenderId: "699872938105",
            appId: "1:699872938105:web:c4c31cde360147caf3aca8",
            measurementId: "G-5R2T1KEEGH"
        };

        // Firebase 초기화
        const app = initializeApp(firebaseConfig);

        // App Check 초기화 (v3 백그라운드)
        const appCheck = initializeAppCheck(app, {
            provider: new ReCaptchaV3Provider('6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S'),
            isTokenAutoRefreshEnabled: true
        });

        console.log('Firebase App Check 초기화 완료');
        window.firebaseApp = app;
        window.appCheck = appCheck;
    </script>

    <!-- reCAPTCHA v2 (체크박스 - 전화번호 인증용) -->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <!-- reCAPTCHA v3 (백그라운드 - App Check용) -->
    <script src="https://www.google.com/recaptcha/api.js?render=6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S" async defer></script>
    <script>
        // Flutter에서 전달받을 사용자 UID
        window.currentUserUID = null;
        window.userInfoReady = false;
        
        // Flutter에서 사용자 정보를 설정하는 함수
        window.setUserInfo = function(uid) {
            window.currentUserUID = uid;
            window.userInfoReady = true;
            console.log('Flutter에서 사용자 UID 설정됨:', uid);
            
            // 사용자 정보가 설정되면 UI 업데이트
            const messageContainer = document.getElementById('messageContainer');
            if (messageContainer) {
                messageContainer.innerHTML = '<div class="message info">사용자 인증 완료. 전화번호를 입력해주세요.</div>';
            }
        };
        
        // reCAPTCHA v3 백그라운드 토큰
        window.recaptchaV3Token = null;

        // reCAPTCHA v3 초기화 및 백그라운드 실행
        function initRecaptchaV3() {
            if (typeof grecaptcha !== 'undefined') {
                grecaptcha.ready(function() {
                    grecaptcha.execute('6LcfOK8rAAAAAPcKb5ByuyWQVHPaJXcGLGRCRs0S', {action: 'page_load'})
                        .then(function(token) {
                            window.recaptchaV3Token = token;
                            console.log('reCAPTCHA v3 백그라운드 토큰 획득 완료');
                        })
                        .catch(function(error) {
                            console.error('reCAPTCHA v3 오류:', error);
                        });
                });
            }
        }

        // 페이지 로드 완료 시 사용자 정보 대기 및 v3 초기화
        document.addEventListener('DOMContentLoaded', function() {
            console.log('페이지 로드 완료, 사용자 정보 대기 중...');

            // reCAPTCHA v3 백그라운드 초기화
            setTimeout(initRecaptchaV3, 1000);

            // 5초 후에도 사용자 정보가 없으면 오류 표시
            setTimeout(function() {
                if (!window.userInfoReady) {
                    const messageContainer = document.getElementById('messageContainer');
                    if (messageContainer) {
                        messageContainer.innerHTML = '<div class="message error">사용자 정보를 불러올 수 없습니다. 앱을 다시 시작해주세요.</div>';
                    }
                }
            }, 5000);
        });
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FAFAFA;
            color: #1F2937;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.02);
            border: 1px solid #F3F4F6;
        }

        .title {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
            text-align: center;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #6B7280;
            text-align: center;
            margin-bottom: 32px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #E09A74;
            box-shadow: 0 0 0 3px rgba(224, 154, 116, 0.1);
        }

        .phone-input-group {
            display: flex;
            gap: 8px;
        }

        .phone-input {
            flex: 1;
        }

        .send-btn {
            padding: 12px 20px;
            background-color: #E09A74;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }

        .send-btn:hover:not(:disabled) {
            background-color: #D08052;
        }

        .send-btn:disabled {
            background-color: #9CA3AF;
            cursor: not-allowed;
        }

        .verify-btn {
            width: 100%;
            padding: 16px;
            background-color: #6366F1;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 16px;
        }

        .verify-btn:hover:not(:disabled) {
            background-color: #5856EB;
        }

        .verify-btn:disabled {
            background-color: #9CA3AF;
            cursor: not-allowed;
        }

        .recaptcha-container {
            margin: 24px 0;
            display: flex;
            justify-content: center;
        }

        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .message.success {
            background-color: #F0FDF4;
            color: #166534;
            border: 1px solid #BBF7D0;
        }

        .message.error {
            background-color: #FEF2F2;
            color: #DC2626;
            border: 1px solid #FECACA;
        }

        .message.info {
            background-color: #F0F9FF;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
        }

        .timer {
            font-size: 12px;
            color: #DC2626;
            margin-top: 4px;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #E09A74;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .container {
                margin: 0 10px;
                padding: 24px;
            }
            
            .phone-input-group {
                flex-direction: column;
            }
            
            .send-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">전화번호 인증</h1>
        <p class="subtitle">본인 확인을 위해 전화번호를 인증해주세요</p>

        <div id="messageContainer"></div>

        <form id="phoneVerificationForm">
            <div class="form-group">
                <label for="phoneNumber" class="form-label">전화번호</label>
                <div class="phone-input-group">
                    <input type="tel" id="phoneNumber" class="form-input phone-input" placeholder="010-1234-5678" maxlength="13">
                    <button type="button" id="sendCodeBtn" class="send-btn">
                        인증번호 발송
                    </button>
                </div>
            </div>

            <!-- reCAPTCHA v2 (기존 키 유지 - 전화번호 인증용) -->
            <div id="recaptchaContainer" class="recaptcha-container">
                <div class="g-recaptcha"
                     data-sitekey="6LdfCK4rAAAAAFjdN1h44yAUhSj4aLeklB3U1DO_"
                     data-callback="onRecaptchaSuccess"
                     data-expired-callback="onRecaptchaExpired"></div>
            </div>

            <div id="verificationGroup" class="form-group hidden">
                <label for="verificationCode" class="form-label">인증번호</label>
                <input type="text" id="verificationCode" class="form-input" placeholder="6자리 인증번호 입력" maxlength="6">
                <div id="timer" class="timer"></div>
                <button type="button" id="verifyBtn" class="verify-btn">
                    인증 확인
                </button>
            </div>
        </form>
    </div>

    <script>
        let isCodeSent = false;
        let countdownTimer = null;
        let timeLeft = 300; // 5분

        // 전화번호 포맷팅
        document.getElementById('phoneNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9]/g, '');
            if (value.length >= 3) {
                if (value.length >= 7) {
                    value = value.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
                } else {
                    value = value.replace(/(\d{3})(\d{4})/, '$1-$2');
                }
            }
            e.target.value = value;
        });

        // 인증번호 숫자만 입력
        document.getElementById('verificationCode').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });

        // 메시지 표시 함수
        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="message ${type}">${message}</div>`;
        }

        // reCAPTCHA 콜백 함수들
        window.onRecaptchaSuccess = function(token) {
            console.log('reCAPTCHA 인증 성공:', token);
            showMessage('reCAPTCHA 인증이 완료되었습니다. 전화번호를 입력하고 발송 버튼을 클릭해주세요.', 'success');
        };

        window.onRecaptchaExpired = function() {
            console.log('reCAPTCHA 만료됨');
            showMessage('reCAPTCHA가 만료되었습니다. 다시 인증해주세요.', 'error');
            grecaptcha.reset();
        };

        // 타이머 시작
        function startTimer() {
            const timerElement = document.getElementById('timer');
            countdownTimer = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerElement.textContent = `남은 시간: ${minutes}:${seconds.toString().padStart(2, '0')}`;

                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    timerElement.textContent = '인증 시간이 만료되었습니다.';
                    document.getElementById('verifyBtn').disabled = true;
                }
                timeLeft--;
            }, 1000);
        }

        // 인증번호 발송
        document.getElementById('sendCodeBtn').addEventListener('click', async function() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const recaptchaResponse = grecaptcha.getResponse();

            if (!phoneNumber) {
                showMessage('전화번호를 입력해주세요.', 'error');
                return;
            }

            if (!recaptchaResponse) {
                showMessage('reCAPTCHA 인증을 완료해주세요.', 'error');
                return;
            }

            console.log('reCAPTCHA 토큰:', recaptchaResponse);

            const phoneRegex = /^010-\d{4}-\d{4}$/;
            if (!phoneRegex.test(phoneNumber)) {
                showMessage('올바른 전화번호 형식으로 입력해주세요. (010-1234-5678)', 'error');
                return;
            }

            this.disabled = true;
            this.innerHTML = '<span class="loading"></span>발송 중...';

            try {
                // 사용자 인증 확인
                if (!window.currentUserUID) {
                    throw new Error('사용자 정보를 불러오는 중입니다. 잠시 후 다시 시도해주세요.');
                }

                // Firebase Functions 호출 (기존 함수명)
                const response = await fetch('https://us-central1-parabara-1a504.cloudfunctions.net/sendSMSRequest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phoneNumber: phoneNumber,
                        recaptchaToken: recaptchaResponse,
                        uid: window.currentUserUID
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('인증번호가 발송되었습니다.', 'success');
                    document.getElementById('verificationGroup').classList.remove('hidden');
                    isCodeSent = true;
                    timeLeft = 300;
                    startTimer();

                    // reCAPTCHA 숨기기 (발송 성공 시)
                    document.getElementById('recaptchaContainer').style.display = 'none';
                } else {
                    throw new Error(result.message || '인증번호 발송에 실패했습니다.');
                }
            } catch (error) {
                console.error('SMS 발송 오류:', error);
                showMessage(error.message || '인증번호 발송에 실패했습니다.', 'error');

                // 오류 시 reCAPTCHA 다시 보이기
                document.getElementById('recaptchaContainer').style.display = 'flex';
                grecaptcha.reset(); // reCAPTCHA 리셋
            } finally {
                this.disabled = false;
                this.innerHTML = '재발송';

                // 재발송 버튼 클릭 시 reCAPTCHA 다시 보이기
                document.getElementById('recaptchaContainer').style.display = 'flex';
            }
        });

        // 인증 확인
        document.getElementById('verifyBtn').addEventListener('click', async function() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const verificationCode = document.getElementById('verificationCode').value;

            if (!verificationCode) {
                showMessage('인증번호를 입력해주세요.', 'error');
                return;
            }

            if (verificationCode.length !== 6) {
                showMessage('6자리 인증번호를 입력해주세요.', 'error');
                return;
            }

            this.disabled = true;
            this.innerHTML = '<span class="loading"></span>인증 중...';

            try {
                // 사용자 인증 확인
                if (!window.currentUserUID) {
                    throw new Error('사용자 정보를 불러오는 중입니다. 잠시 후 다시 시도해주세요.');
                }

                // Firebase Functions 호출 (기존 함수명)
                const response = await fetch('https://us-central1-parabara-1a504.cloudfunctions.net/verifySMSRequest', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phoneNumber: phoneNumber,
                        code: verificationCode,
                        uid: window.currentUserUID
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('전화번호 인증이 완료되었습니다!', 'success');
                    clearInterval(countdownTimer);

                    // Flutter 앱으로 결과 전달 및 웹뷰 자동 종료
                    if (window.phoneVerificationSuccess) {
                        window.phoneVerificationSuccess.postMessage(JSON.stringify({
                            phoneNumber: phoneNumber,
                            verified: true
                        }));
                    }

                    // 잠시 후 웹뷰 종료 시도 (Flutter에서 처리)
                    setTimeout(() => {
                        if (window.phoneVerificationSuccess) {
                            window.phoneVerificationSuccess.postMessage(JSON.stringify({
                                action: 'close',
                                phoneNumber: phoneNumber,
                                verified: true
                            }));
                        }
                    }, 1500);
                } else {
                    throw new Error(result.message || '인증에 실패했습니다.');
                }
            } catch (error) {
                console.error('인증 확인 오류:', error);
                showMessage(error.message || '인증에 실패했습니다.', 'error');
            } finally {
                this.disabled = false;
                this.innerHTML = '인증 확인';
            }
        });

        // Enter 키 처리
        document.getElementById('phoneNumber').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('sendCodeBtn').click();
            }
        });

        // 인증번호 입력 시 자동 포맷팅
        document.getElementById('verificationCode').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });

        document.getElementById('verificationCode').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('verifyBtn').click();
            }
        });
    </script>
</body>
</html>
