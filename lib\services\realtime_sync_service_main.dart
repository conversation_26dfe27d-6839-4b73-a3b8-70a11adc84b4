/// 실시간 동기화 서비스 (메인 버전)
///
/// Firebase Firestore를 사용하여 실시간 데이터 동기화를 제공합니다.
/// 실제로 데이터 변경을 UI에 전달하는 완전한 구현입니다.
///
/// 작성자: Blue
/// 버전: 2.0.0
/// 최종 업데이트: 2025년 7월

import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import '../models/seller.dart';
import '../models/prepayment.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/prepayment_product_link.dart';
import '../models/sales_log.dart';
import '../models/set_discount.dart';
import '../models/event.dart';
import '../models/category.dart' as model_category;
import '../models/user_settings.dart';
import '../models/checklist_template.dart';
import '../models/checklist_item.dart';
import '../utils/logger_utils.dart';
import '../utils/image_sync_utils.dart';
import '../utils/firebase_upload_utils.dart';
import '../utils/local_data_cleaner.dart';
import '../utils/merge_util.dart';
import 'subscription_service.dart';

/// 실시간 데이터 변경 타입
enum RealtimeChangeType { added, modified, removed }

/// 실시간 데이터 변경 정보
class RealtimeDataChange {
  final String collectionName;
  final String documentId;
  final RealtimeChangeType changeType;
  final Map<String, dynamic>? data;
  final int eventId;

  const RealtimeDataChange({
    required this.collectionName,
    required this.documentId,
    required this.changeType,
    this.data,
    required this.eventId,
  });
}

/// 이벤트 데이터 모음
class EventRealtimeData {
  final List<Product> products;
  final List<Seller> sellers;
  final List<Prepayment> prepayments;
  final List<SalesLog> salesLogs;
  final List<SetDiscount> setDiscounts;
  final List<model_category.Category> categories;
  final List<ChecklistItem> checklistItems;

  const EventRealtimeData({
    this.products = const [],
    this.sellers = const [],
    this.prepayments = const [],
    this.salesLogs = const [],
    this.setDiscounts = const [],
    this.categories = const [],
    this.checklistItems = const [],
  });

  EventRealtimeData copyWith({
    List<Product>? products,
    List<Seller>? sellers,
    List<Prepayment>? prepayments,
    List<SalesLog>? salesLogs,
    List<SetDiscount>? setDiscounts,
    List<model_category.Category>? categories,
    List<ChecklistItem>? checklistItems,
  }) {
    return EventRealtimeData(
      products: products ?? this.products,
      sellers: sellers ?? this.sellers,
      prepayments: prepayments ?? this.prepayments,
      salesLogs: salesLogs ?? this.salesLogs,
      setDiscounts: setDiscounts ?? this.setDiscounts,
      categories: categories ?? this.categories,
      checklistItems: checklistItems ?? this.checklistItems,
    );
  }
}

/// 전체 사용자 데이터 (모든 이벤트 포함)
class UserRealtimeData {
  final List<Event> events;
  final Map<int, EventRealtimeData> eventData;
  final List<ChecklistTemplate> checklistTemplates;

  const UserRealtimeData({
    this.events = const [],
    this.eventData = const {},
    this.checklistTemplates = const [],
  });

  UserRealtimeData copyWith({
    List<Event>? events,
    Map<int, EventRealtimeData>? eventData,
    List<ChecklistTemplate>? checklistTemplates,
  }) {
    return UserRealtimeData(
      events: events ?? this.events,
      eventData: eventData ?? this.eventData,
      checklistTemplates: checklistTemplates ?? this.checklistTemplates,
    );
  }
}

class RealtimeSyncService {
  static const String _tag = 'RealtimeSyncService';
  
  // Firebase 인스턴스
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Connectivity _connectivity = Connectivity();
  
  // 연결 상태 관리
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  
  // 인증 상태 구독
  StreamSubscription<User?>? _authStateSubscription;
  
  final ValueNotifier<bool> _isConnected = ValueNotifier(false);
  final ValueNotifier<bool> _isInitialized = ValueNotifier(false);
  
  // 데이터 스트림 컨트롤러
  final StreamController<RealtimeDataChange> _dataChangeController = 
      StreamController<RealtimeDataChange>.broadcast();
  
  // 이벤트별 데이터 캐시
  final Map<int, EventRealtimeData> _eventDataCache = {};
  final Map<int, StreamController<EventRealtimeData>> _eventDataControllers = {};
  
  // 활성 구독 관리
  final Map<String, StreamSubscription> _activeListeners = {};
  final ValueNotifier<int> _activeSubscriptions = ValueNotifier(0);
  
  // 사용자 문서 삭제 감지를 위한 구독
  StreamSubscription<DocumentSnapshot>? _userDocumentSubscription;

  // 사용자 설정 실시간 구독
  StreamSubscription<DocumentSnapshot>? _userSettingsSubscription;
  final StreamController<UserSettings?> _userSettingsController =
      StreamController<UserSettings?>.broadcast();

  // 로그아웃 처리 중인지 여부 (중복 처리 방지)
  bool _isLoggingOut = false;

  // 실시간 동기화 활성화 여부 (기본값: false, 사용자가 수동으로 활성화)
  bool _realtimeSyncEnabled = false;

  // 🔥 앱 포그라운드 상태 (최적화용)
  bool _isAppInForeground = true;
  
  // Getters
  Stream<RealtimeDataChange> get dataChanges => _dataChangeController.stream;
  Stream<UserSettings?> get userSettingsChanges => _userSettingsController.stream;
  ValueNotifier<bool> get isConnected => _isConnected;
  ValueNotifier<bool> get isInitialized => _isInitialized;
  ValueNotifier<int> get activeSubscriptions => _activeSubscriptions;
  bool get realtimeSyncEnabled => _realtimeSyncEnabled;
  
  /// 실시간 동기화 활성화/비활성화
  Future<void> setRealtimeSyncEnabled(bool enabled) async {
    try {
      LoggerUtils.logInfo('실시간 동기화 설정 변경: $enabled', tag: _tag);

      _realtimeSyncEnabled = enabled;

      if (enabled) {
        // 동기화 활성화 - 기존 구독들을 다시 시작
        if (_isInitialized.value) {
          _restartAllSubscriptions();
          // 전역 데이터 구독도 다시 시작
          await _subscribeToEventsCollection();
          await _subscribeToChecklistTemplatesCollection();
        }
      } else {
        // 동기화 비활성화 - 모든 구독 취소
        _cancelAllSubscriptions();
      }

      LoggerUtils.logInfo('실시간 동기화 설정 변경 완료: $enabled', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError('실시간 동기화 설정 변경 실패', tag: _tag, error: e, stackTrace: stackTrace);
    }
  }

  /// 서비스 초기화
  Future<void> initialize() async {
    try {
      LoggerUtils.logInfo('실시간 동기화 서비스 초기화 시작', tag: _tag);

      // 네트워크 상태 모니터링 시작
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);

      // 인증 상태 변경 모니터링
      _authStateSubscription = _auth.authStateChanges().listen(_onAuthStateChanged);

      // 현재 사용자가 있으면 사용자 문서 감지 시작
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        _startUserDocumentListener(currentUser.uid);
        _startUserSettingsSubscription(currentUser.uid);
      }

      // 초기 연결 상태 확인
      final connectivityResult = await _connectivity.checkConnectivity();
      _isConnected.value = !connectivityResult.contains(ConnectivityResult.none);

      // 실시간 동기화가 활성화된 경우에만 구독 시작
      if (_realtimeSyncEnabled) {
        // Events 컬렉션 구독 (전역 데이터)
        await _subscribeToEventsCollection();

        // 체크리스트 템플릿 구독 (전역 데이터)
        await _subscribeToChecklistTemplatesCollection();
      }

      _isInitialized.value = true;
      LoggerUtils.logInfo('실시간 동기화 서비스 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('실시간 동기화 서비스 초기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 이벤트 구독 시작
  Future<void> subscribeToEvent(int eventId) async {
    if (!_isInitialized.value) {
      throw Exception('서비스가 초기화되지 않았습니다. initialize()를 먼저 호출하세요.');
    }

    if (!_realtimeSyncEnabled) {
      LoggerUtils.logInfo('실시간 동기화가 비활성화되어 이벤트 $eventId 구독을 건너뜁니다', tag: _tag);
      return;
    }

    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('사용자가 로그인되지 않았습니다.');
    }
    
    try {
      LoggerUtils.logInfo('이벤트 $eventId 구독 시작', tag: _tag);

      // 기존 이벤트 구독이 있다면 먼저 정리
      await _unsubscribeFromEvent(eventId);

      // 이벤트 데이터 컨트롤러 생성
      if (!_eventDataControllers.containsKey(eventId)) {
        _eventDataControllers[eventId] = StreamController<EventRealtimeData>.broadcast();
        _eventDataCache[eventId] = const EventRealtimeData();
      }

      // Tombstone(삭제 표시) 7일 경과분 정리 - 구독 전에 가볍게 수행(있을 때만 삭제)
      try {
        await _cleanupOldTombstones(eventId);
      } catch (e) {
        LoggerUtils.logWarning('Tombstone 정리 중 경고: $e', tag: _tag);
      }

      // 각 컬렉션 구독
      await _subscribeToCollection(eventId, 'products');
      await _subscribeToCollection(eventId, 'sellers');
      await _subscribeToCollection(eventId, 'categories');
      await _subscribeToCollection(eventId, 'prepayments');
      await _subscribeToCollection(eventId, 'prepayment_virtual_products');
      await _subscribeToCollection(eventId, 'prepayment_product_links');
      await _subscribeToCollection(eventId, 'sales_logs');
      await _subscribeToCollection(eventId, 'set_discounts');
      await _subscribeToCollection(eventId, 'checklist_items');
      await _subscribeToCollection(eventId, 'revenue_goals');

      LoggerUtils.logInfo('이벤트 $eventId 구독 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 $eventId 구독 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 이벤트 데이터 스트림 가져오기
  Stream<EventRealtimeData>? getEventDataStream(int eventId) {
    return _eventDataControllers[eventId]?.stream;
  }
  
  /// 이벤트 현재 데이터 가져오기
  EventRealtimeData? getCurrentEventData(int eventId) {
    return _eventDataCache[eventId];
  }

  /// 특정 이벤트의 모든 구독 해제
  Future<void> _unsubscribeFromEvent(int eventId) async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      LoggerUtils.logInfo('이벤트 $eventId 구독 해제 시작', tag: _tag);

      final collections = [
        'products', 'sellers', 'categories', 'prepayments',
        'prepayment_virtual_products', 'prepayment_product_links',
        'sales_logs', 'set_discounts', 'checklist_items', 'revenue_goals'
      ];

      for (final collectionName in collections) {
        final subscriptionKey = '${user.uid}_event_${eventId}_$collectionName';

        if (_activeListeners.containsKey(subscriptionKey)) {
          await _activeListeners[subscriptionKey]?.cancel();
          _activeListeners.remove(subscriptionKey);
          LoggerUtils.logDebug('$collectionName 구독 해제됨: $eventId', tag: _tag);
        }
      }

      _activeSubscriptions.value = _activeListeners.length;
      LoggerUtils.logInfo('이벤트 $eventId 구독 해제 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 $eventId 구독 해제 실패', tag: _tag, error: e);
    }
  }
  
  /// 컬렉션 구독
  Future<void> _subscribeToCollection(int eventId, String collectionName) async {
    if (!_realtimeSyncEnabled) {
      LoggerUtils.logDebug('실시간 동기화가 비활성화되어 $collectionName 구독을 건너뜁니다', tag: _tag);
      return;
    }

    final user = _auth.currentUser;
    if (user == null) return;
    
    final subscriptionKey = '${user.uid}_event_${eventId}_$collectionName';
    
    // 이미 구독 중이면 건너뛰기
    if (_activeListeners.containsKey(subscriptionKey)) {
      LoggerUtils.logDebug('$collectionName 이미 구독 중: $eventId', tag: _tag);
      return;
    }
    
    try {
      final collectionRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection(collectionName);

      final subscription = collectionRef.snapshots().listen(
        (snapshot) => _handleSnapshot(snapshot, collectionName, eventId),
        onError: (error) => _handleSubscriptionError(collectionName, eventId, error),
      );

      _activeListeners[subscriptionKey] = subscription;
      _activeSubscriptions.value = _activeListeners.length;
      LoggerUtils.logInfo('$collectionName 구독 시작됨: $eventId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('$collectionName 구독 실패: $eventId', tag: _tag, error: e);
      rethrow;
    }
  }

  /// Events 컬렉션 구독 (전역 데이터)
  Future<void> _subscribeToEventsCollection() async {
    if (!_realtimeSyncEnabled) {
      LoggerUtils.logDebug('실시간 동기화가 비활성화되어 Events 구독을 건너뜁니다', tag: _tag);
      return;
    }

    final user = _auth.currentUser;
    if (user == null) return;

    final subscriptionKey = '${user.uid}_events';

    // 이미 구독 중이면 건너뛰기
    if (_activeListeners.containsKey(subscriptionKey)) {
      LoggerUtils.logDebug('Events 이미 구독 중', tag: _tag);
      return;
    }

    try {
      final eventsRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events');

      final subscription = eventsRef.snapshots().listen(
        (snapshot) => _handleEventsSnapshot(snapshot),
        onError: (error) => _handleEventsSubscriptionError(error),
      );

      _activeListeners[subscriptionKey] = subscription;
      _activeSubscriptions.value = _activeListeners.length;
      LoggerUtils.logInfo('Events 컬렉션 구독 시작됨', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Events 컬렉션 구독 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 체크리스트 템플릿 컬렉션 구독 (전역 데이터)
  Future<void> _subscribeToChecklistTemplatesCollection() async {
    if (!_realtimeSyncEnabled) {
      LoggerUtils.logDebug('실시간 동기화가 비활성화되어 체크리스트 템플릿 구독을 건너뜁니다', tag: _tag);
      return;
    }

    final user = _auth.currentUser;
    if (user == null) return;

    final subscriptionKey = '${user.uid}_checklist_templates';

    // 이미 구독 중이면 건너뛰기
    if (_activeListeners.containsKey(subscriptionKey)) {
      LoggerUtils.logDebug('체크리스트 템플릿 이미 구독 중', tag: _tag);
      return;
    }

    try {
      final templatesRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('checklist_templates');

      final subscription = templatesRef.snapshots().listen(
        (snapshot) => _handleChecklistTemplatesSnapshot(snapshot),
        onError: (error) => _handleChecklistTemplatesSubscriptionError(error),
      );

      _activeListeners[subscriptionKey] = subscription;
      _activeSubscriptions.value = _activeListeners.length;
      LoggerUtils.logInfo('체크리스트 템플릿 컬렉션 구독 시작됨', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 컬렉션 구독 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 스냅샷 처리
  Future<void> _handleSnapshot(QuerySnapshot snapshot, String collectionName, int eventId) async {
    try {
      LoggerUtils.logInfo('$collectionName 스냅샷 수신: ${snapshot.docs.length}개 문서', tag: _tag);
      
      // 변경사항 처리
      for (final change in snapshot.docChanges) {
        await _processDocumentChange(change, collectionName, eventId);
      }
      
      // 전체 데이터 업데이트
      await _updateEventData(eventId, collectionName, snapshot);
      
      LoggerUtils.logInfo('$collectionName 스냅샷 처리 완료: $eventId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('$collectionName 스냅샷 처리 실패: $eventId', tag: _tag, error: e);
    }
  }
  
  /// 문서 변경사항 처리
  Future<void> _processDocumentChange(DocumentChange change, String collectionName, int eventId) async {
    try {
      final docId = change.doc.id;
      final data = change.doc.data() as Map<String, dynamic>?;
      
      final changeType = switch (change.type) {
        DocumentChangeType.added => RealtimeChangeType.added,
        DocumentChangeType.modified => RealtimeChangeType.modified,
        DocumentChangeType.removed => RealtimeChangeType.removed,
      };
      
      // 데이터 변경 이벤트 발생
      final changeEvent = RealtimeDataChange(
        collectionName: collectionName,
        documentId: docId,
        changeType: changeType,
        data: data,
        eventId: eventId,
      );
      
      _dataChangeController.add(changeEvent);
      
      LoggerUtils.logInfo('$collectionName 문서 ${changeType.name}: $docId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('$collectionName 변경사항 처리 실패', tag: _tag, error: e);
    }
  }

  /// Events 스냅샷 처리
  Future<void> _handleEventsSnapshot(QuerySnapshot snapshot) async {
    try {
      LoggerUtils.logInfo('Events 스냅샷 수신: ${snapshot.docs.length}개 문서', tag: _tag);
      
      // 변경사항 처리
      for (final change in snapshot.docChanges) {
        await _processEventsDocumentChange(change);
      }
      
      LoggerUtils.logInfo('Events 스냅샷 처리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Events 스냅샷 처리 실패', tag: _tag, error: e);
    }
  }

  /// Events 문서 변경사항 처리
  Future<void> _processEventsDocumentChange(DocumentChange change) async {
    try {
      final docId = change.doc.id;
      final data = change.doc.data() as Map<String, dynamic>?;
      
      final changeType = switch (change.type) {
        DocumentChangeType.added => RealtimeChangeType.added,
        DocumentChangeType.modified => RealtimeChangeType.modified,
        DocumentChangeType.removed => RealtimeChangeType.removed,
      };
      
      // Events 변경 이벤트 발생 (eventId는 0으로 설정하여 전역 이벤트임을 표시)
      final changeEvent = RealtimeDataChange(
        collectionName: 'events',
        documentId: docId,
        changeType: changeType,
        data: data,
        eventId: 0, // 전역 이벤트
      );
      
      _dataChangeController.add(changeEvent);
      
      LoggerUtils.logInfo('Events 문서 ${changeType.name}: $docId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Events 변경사항 처리 실패', tag: _tag, error: e);
    }
  }

  /// Events 구독 오류 처리
  void _handleEventsSubscriptionError(dynamic error) {
    LoggerUtils.logError('Events 구독 오류', tag: _tag, error: error);
  }

  /// 체크리스트 템플릿 스냅샷 처리
  Future<void> _handleChecklistTemplatesSnapshot(QuerySnapshot snapshot) async {
    try {
      LoggerUtils.logInfo('체크리스트 템플릿 스냅샷 수신: ${snapshot.docs.length}개 문서', tag: _tag);

      // 변경사항 처리
      for (final change in snapshot.docChanges) {
        await _processChecklistTemplatesDocumentChange(change);
      }

      LoggerUtils.logInfo('체크리스트 템플릿 스냅샷 처리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 스냅샷 처리 실패', tag: _tag, error: e);
    }
  }

  /// 체크리스트 템플릿 문서 변경사항 처리
  Future<void> _processChecklistTemplatesDocumentChange(DocumentChange change) async {
    try {
      final docId = change.doc.id;
      final data = change.doc.data() as Map<String, dynamic>?;

      final changeType = switch (change.type) {
        DocumentChangeType.added => RealtimeChangeType.added,
        DocumentChangeType.modified => RealtimeChangeType.modified,
        DocumentChangeType.removed => RealtimeChangeType.removed,
      };

      // 체크리스트 템플릿 변경 이벤트 발생 (eventId는 0으로 설정하여 전역 이벤트임을 표시)
      final changeEvent = RealtimeDataChange(
        collectionName: 'checklist_templates',
        documentId: docId,
        changeType: changeType,
        data: data,
        eventId: 0, // 전역 이벤트
      );

      _dataChangeController.add(changeEvent);

      LoggerUtils.logInfo('체크리스트 템플릿 문서 ${changeType.name}: $docId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 변경사항 처리 실패', tag: _tag, error: e);
    }
  }

  /// Tombstone(삭제 표시) 7일 경과분 정리 (서버/로컬 과다 조회 없이 최소 범위로)
  Future<void> _cleanupOldTombstones(int eventId) async {
    final user = _auth.currentUser;
    if (user == null) return;
    try {
      // 하루 1회 제한 체크 (SharedPreferences)
      final prefs = await SharedPreferences.getInstance();
      final key = 'tombstone_cleanup_last_${eventId}';
      final lastIso = prefs.getString(key);
      if (lastIso != null) {
        final last = DateTime.tryParse(lastIso);
        if (last != null && DateTime.now().difference(last).inHours < 24) {
          LoggerUtils.logDebug('Tombstone 정리 스킵(24시간 이내): event $eventId', tag: _tag);
          return;
        }
      }

      // 🔥 최적화: 백그라운드일 때만 감지 중단
      final collections = [
        'products','sellers','categories','prepayments',
        'prepayment_virtual_products','prepayment_product_links','revenue_goals',
        'sales_logs','set_discounts','checklist_items'
      ];

      // 앱이 백그라운드에 있으면 실시간 감지 중단 (DB 사용량 절약)
      if (!_isAppInForeground) {
        print('📱 앱이 백그라운드 상태 - 실시간 동기화 일시 중단');
        return;
      }
      final threshold = DateTime.now().toUtc().subtract(const Duration(days: 7));

      for (final col in collections) {
        try {
          final colRef = _firestore
              .collection('users').doc(user.uid)
              .collection('events').doc(eventId.toString())
              .collection(col);

          // isDeleted=true && updatedAt < threshold 인 문서만 제한적으로 가져와 삭제
          final snap = await colRef
              .where('isDeleted', isEqualTo: true)
              .where('updatedAt', isLessThan: Timestamp.fromDate(threshold))
              .limit(20)
              .get();

          for (final doc in snap.docs) {
            try {
              await colRef.doc(doc.id).delete();
            } catch (e) {
              LoggerUtils.logWarning('Tombstone 삭제 실패: $col/${doc.id}', tag: _tag, error: e);
            }
          }
        } catch (e) {
          // Firestore 인덱스 오류 등으로 쿼리 실패 시 무시하고 계속 진행
          final errorString = e.toString();
          if (errorString.contains('FAILED_PRECONDITION') ||
              errorString.contains('requires an index')) {
            LoggerUtils.logInfo('Tombstone 정리 쿼리 인덱스 없음 - 건너뛰기: $col', tag: _tag);
          } else {
            LoggerUtils.logWarning('Tombstone 정리 쿼리 실패: $col', tag: _tag, error: e);
          }
        }
      }

      // 마지막 실행 시각 기록(하루 1회 제한)
      await prefs.setString(key, DateTime.now().toIso8601String());
    } catch (e) {
      LoggerUtils.logWarning('Tombstone 정리 중 예외', tag: _tag, error: e);
    }
  }

  /// 체크리스트 템플릿 구독 오류 처리
  void _handleChecklistTemplatesSubscriptionError(dynamic error) {
    LoggerUtils.logError('체크리스트 템플릿 구독 오류', tag: _tag, error: error);
  }

  /// 이벤트 데이터 업데이트
  Future<void> _updateEventData(int eventId, String collectionName, QuerySnapshot snapshot) async {
    try {
      final currentData = _eventDataCache[eventId] ?? const EventRealtimeData();
      EventRealtimeData updatedData = currentData;
      
      switch (collectionName) {
        case 'products':
          final products = <Product>[];
          for (final doc in snapshot.docs) {
            final raw = doc.data() as Map<String, dynamic>;
            raw['id'] = int.tryParse(doc.id) ?? 0;
            raw['eventId'] = eventId;

            // 로컬 캐시에서 동일 id 상품 찾기
            Product? localItem;
            for (final e in currentData.products) {
              if ((e.id ?? 0) == raw['id']) { localItem = e; break; }
            }
            final localMap = localItem != null ? (localItem.toJson()..['eventId']=eventId) : <String, dynamic>{};

            // 충돌/시간 비교 로그 (DB 사용 증가 없음)
            final cmp = MergeUtil.compareByTimestamp(localMap, raw);
            if (MergeUtil.isTombstone(raw)) {
              LoggerUtils.logInfo('products tombstone detected: id=${raw['id']}', tag: _tag);
              continue; // 삭제 표시는 목록에서 제외
            }

            final data = MergeUtil.mergeMaps(localMap, raw);
            var product = Product.fromJson(data);

            // 이미지가 네트워크 URL인 경우 다운로드 처리
            product = await _processProductWithImage(eventId, product);
            products.add(product);

            if (cmp == 0 && localItem != null) {
              LoggerUtils.logDebug('products field-merge applied: id=${raw['id']}', tag: _tag);
            }
          }
          updatedData = currentData.copyWith(products: products);
          break;
          
        case 'sellers':
          final sellers = snapshot.docs.map((doc) {
            final raw = doc.data() as Map<String, dynamic>;
            raw['id'] = int.tryParse(doc.id) ?? 0;
            raw['eventId'] = eventId;
            final data = MergeUtil.mergeMaps({}, raw);
            return Seller.fromJson(data);
          }).toList();
          updatedData = currentData.copyWith(sellers: sellers);
          break;
          
        case 'prepayments':
          final prepayments = snapshot.docs.map((doc) {
            final raw = doc.data() as Map<String, dynamic>;
            raw['id'] = int.tryParse(doc.id) ?? 0;
            raw['eventId'] = eventId;
            final data = MergeUtil.mergeMaps({}, raw);
            return Prepayment.fromJson(data);
          }).toList();
          updatedData = currentData.copyWith(prepayments: prepayments);
          break;
          
        case 'sales_logs':
          final salesLogs = snapshot.docs.map((doc) {
            final raw = doc.data() as Map<String, dynamic>;
            raw['id'] = int.tryParse(doc.id) ?? 0;
            raw['eventId'] = eventId;
            final data = MergeUtil.mergeMaps({}, raw);
            return SalesLog.fromJson(data);
          }).toList();
          updatedData = currentData.copyWith(salesLogs: salesLogs);
          break;
          
        case 'categories':
          final categories = snapshot.docs.map((doc) {
            final raw = doc.data() as Map<String, dynamic>;
            raw['id'] = int.tryParse(doc.id) ?? 0;
            raw['eventId'] = eventId;
            final data = MergeUtil.mergeMaps({}, raw);
            return model_category.Category.fromJson(data);
          }).toList();
          updatedData = currentData.copyWith(categories: categories);
          break;

        case 'set_discounts':
          final setDiscounts = snapshot.docs.map((doc) {
            final raw = doc.data() as Map<String, dynamic>;
            raw['id'] = int.tryParse(doc.id) ?? 0;
            raw['eventId'] = eventId;
            final data = MergeUtil.mergeMaps({}, raw);
            return SetDiscount.fromJson(data);
          }).toList();
          updatedData = currentData.copyWith(setDiscounts: setDiscounts);
          break;

        case 'checklist_items':
          final checklistItems = snapshot.docs.map((doc) {
            final raw = doc.data() as Map<String, dynamic>;
            raw['id'] = int.tryParse(doc.id) ?? 0;
            raw['eventId'] = eventId;
            final data = MergeUtil.mergeMaps({}, raw);
            return ChecklistItem.fromJson({...data});
          }).toList();
          updatedData = currentData.copyWith(checklistItems: checklistItems);
          break;
      }
      
      // 캐시 업데이트
      _eventDataCache[eventId] = updatedData;
      
      // 스트림에 새 데이터 전송
      _eventDataControllers[eventId]?.add(updatedData);
      
    } catch (e) {
      LoggerUtils.logError('이벤트 데이터 업데이트 실패: $eventId', tag: _tag, error: e);
    }
  }
  
  /// 구독 오류 처리
  void _handleSubscriptionError(String collectionName, int eventId, dynamic error) {
    LoggerUtils.logError('$collectionName 구독 오류: $eventId', tag: _tag, error: error);
  }
  
  /// 네트워크 상태 변경 핸들러
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    final wasConnected = _isConnected.value;
    _isConnected.value = !results.contains(ConnectivityResult.none);
    
    if (wasConnected != _isConnected.value) {
      LoggerUtils.logInfo('네트워크 상태 변경: ${_isConnected.value ? "연결됨" : "연결 끊어짐"}', tag: _tag);
      
      if (_isConnected.value) {
        // 연결되면 구독 재시작
        _restartAllSubscriptions();

        // 네트워크 재연결시 플랜 변경 확인
        _checkPlanChangesOnReconnect();
      }
    }
  }
  
  /// 네트워크 재연결시 플랜 변경 확인
  void _checkPlanChangesOnReconnect() async {
    try {
      final subscriptionService = SubscriptionService();
      await subscriptionService.checkPlanChangesOnNetworkReconnect();
    } catch (e) {
      LoggerUtils.logError('네트워크 재연결시 플랜 확인 실패', tag: _tag, error: e);
    }
  }

  /// 인증 상태 변경 핸들러
  void _onAuthStateChanged(User? user) async {
    if (user == null) {
      // 이미 로그아웃 처리 중이면 중복 실행 방지
      if (_isLoggingOut) {
        LoggerUtils.logInfo('이미 로그아웃 처리 중 - _onAuthStateChanged 중복 실행 방지', tag: _tag);
        return;
      }
      
      LoggerUtils.logInfo('사용자 로그아웃됨 - 모든 구독 취소', tag: _tag);
      
      // 사용자 문서 리스너 중지
      _stopUserDocumentListener();

      // 사용자 설정 구독 중지
      _stopUserSettingsSubscription();

      _cancelAllSubscriptions();

      // 다른 기기에서 회원탈퇴했는지 확인
      await _checkAccountDeletionAndCleanup();
    } else {
      LoggerUtils.logInfo('사용자 로그인됨: ${user.email}', tag: _tag);

      // 사용자 문서 감지 시작
      _startUserDocumentListener(user.uid);

      // 사용자 설정 구독 시작
      _startUserSettingsSubscription(user.uid);

      // 기존 구독들 재시작 (로그인 후)
      if (_eventDataControllers.isNotEmpty) {
        LoggerUtils.logInfo('로그인 후 기존 구독들 재시작', tag: _tag);
        _restartAllSubscriptions();
      }

      // 로그인 상태 플래그 설정
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('was_logged_in', true);
      } catch (e) {
        LoggerUtils.logError('로그인 상태 플래그 설정 실패', tag: _tag, error: e);
      }
    }
  }

  /// 사용자 문서 리스너 시작 (계정 삭제 감지용)
  void _startUserDocumentListener(String userId) {
    try {
      LoggerUtils.logInfo('사용자 문서 감지 시작: $userId', tag: _tag);
      
      // 기존 구독이 있으면 취소
      _stopUserDocumentListener();
      
      // 사용자 문서 변경 감지
      _userDocumentSubscription = _firestore
          .collection('users')
          .doc(userId)
          .snapshots()
          .listen(
            (snapshot) async {
              if (!snapshot.exists) {
                LoggerUtils.logWarning('사용자 문서가 삭제됨 - 계정 삭제로 인한 완전 로그아웃 실행', tag: _tag);
                await _handleAccountDeletionWithLogout();
              }
            },
            onError: (error) {
              LoggerUtils.logError('사용자 문서 리스너 오류', tag: _tag, error: error);
            },
          );
    } catch (e) {
      LoggerUtils.logError('사용자 문서 리스너 시작 실패', tag: _tag, error: e);
    }
  }
  
  /// 사용자 문서 리스너 중지
  void _stopUserDocumentListener() {
    _userDocumentSubscription?.cancel();
    _userDocumentSubscription = null;
  }

  /// 다른 기기에서 회원탈퇴 시 로컬 데이터 정리
  Future<void> _checkAccountDeletionAndCleanup() async {
    try {
      // 현재 사용자가 null이고, 이전에 로그인 상태였다면 계정 삭제 가능성 확인
      final prefs = await SharedPreferences.getInstance();
      final wasLoggedIn = prefs.getBool('was_logged_in') ?? false;

      if (wasLoggedIn) {
        LoggerUtils.logInfo('이전 로그인 상태에서 로그아웃됨 - 계정 삭제 여부 확인', tag: _tag);

        // 잠시 대기 후 계정 삭제로 간주하고 로컬 데이터 정리
        await Future.delayed(const Duration(seconds: 1));
        await _handleAccountDeletion();
      }

      // 로그인 상태 플래그 제거
      await prefs.remove('was_logged_in');
    } catch (e) {
      LoggerUtils.logError('계정 삭제 확인 중 오류', tag: _tag, error: e);
    }
  }

  /// 계정 삭제 감지 시 Firebase 로그아웃과 로컬 데이터 정리를 함께 수행
  Future<void> _handleAccountDeletionWithLogout() async {
    // 중복 처리 방지
    if (_isLoggingOut) {
      LoggerUtils.logInfo('이미 로그아웃 처리 중 - 중복 실행 방지', tag: _tag);
      return;
    }
    
    try {
      _isLoggingOut = true;
      LoggerUtils.logInfo('🗑️ 계정 삭제 감지 - Firebase 로그아웃 및 로컬 데이터 정리 시작', tag: _tag);

      // 1. 사용자 문서 리스너 중지
      _stopUserDocumentListener();

      // 2. 회원 탈퇴 진행 중인지 확인 (중복 처리 방지)
      try {
        final prefs = await SharedPreferences.getInstance();
        final isWithdrawing = prefs.getBool('account_withdrawal_in_progress') ?? false;
        if (isWithdrawing) {
          LoggerUtils.logInfo('회원 탈퇴 진행 중 - 실시간 동기화 서비스에서 중복 처리 방지', tag: _tag);
          return;
        }
      } catch (e) {
        LoggerUtils.logWarning('회원 탈퇴 상태 확인 실패 (계속 진행): $e', tag: _tag);
      }

      // 3. Firebase 로그아웃 (이렇게 해야 _onAuthStateChanged에서 중복 처리되지 않음)
      await FirebaseAuth.instance.signOut();
      
      // 4. 로컬 데이터 완전 삭제 (계정 삭제용)
      await LocalDataCleaner.clearDataForAccountDeletion();

      LoggerUtils.logInfo('✅ 계정 삭제로 인한 완전 로그아웃 및 데이터 정리 완료', tag: _tag);

      // 5. SharedPreferences에 계정 삭제 플래그 설정
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('account_deleted_by_other_device', true);
      } catch (e) {
        LoggerUtils.logError('계정 삭제 플래그 설정 실패', tag: _tag, error: e);
      }

    } catch (e) {
      LoggerUtils.logError('계정 삭제 처리 중 오류', tag: _tag, error: e);
    } finally {
      _isLoggingOut = false;
    }
  }

  /// 계정 삭제 시 로컬 데이터 정리 (Firebase 로그아웃 없이)
  Future<void> _handleAccountDeletion() async {
    try {
      LoggerUtils.logInfo('🗑️ 계정 삭제 감지 - 로컬 데이터 정리 시작', tag: _tag);

      // 사용자 문서 리스너 중지
      _stopUserDocumentListener();

      // 로컬 데이터 완전 삭제
      await LocalDataCleaner.clearDataForAccountDeletion();

      LoggerUtils.logInfo('✅ 계정 삭제로 인한 로컬 데이터 정리 완료', tag: _tag);

      // SharedPreferences에 계정 삭제 플래그 설정
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('account_deleted_by_other_device', true);
      } catch (e) {
        LoggerUtils.logError('계정 삭제 플래그 설정 실패', tag: _tag, error: e);
      }

    } catch (e) {
      LoggerUtils.logError('계정 삭제 처리 중 오류', tag: _tag, error: e);
    }
  }
  
  /// 모든 구독 재시작
  void _restartAllSubscriptions() {
    LoggerUtils.logInfo('모든 구독 재시작', tag: _tag);
    
    // 현재 사용자가 로그인되어 있는지 확인
    final user = _auth.currentUser;
    if (user == null) {
      LoggerUtils.logWarning('사용자가 로그인되지 않아 구독 재시작을 건너뜁니다', tag: _tag);
      return;
    }
    
    // 활성화된 이벤트들에 대해 구독 재시작
    final eventIds = _eventDataControllers.keys.toList();
    
    for (final eventId in eventIds) {
      try {
        LoggerUtils.logInfo('이벤트 $eventId 구독 재시작', tag: _tag);
        
        // 기존 구독 정리 (해당 이벤트의 구독만)
        final keysToRemove = _activeListeners.keys
            .where((key) => key.contains('event_$eventId'))
            .toList();
        
        for (final key in keysToRemove) {
          _activeListeners[key]?.cancel();
          _activeListeners.remove(key);
        }
        
        // 구독 재시작 (비동기로 처리하여 블로킹 방지)
        Future.microtask(() async {
          try {
            await _subscribeToCollection(eventId, 'products');
            await _subscribeToCollection(eventId, 'sellers');
            await _subscribeToCollection(eventId, 'categories');
            await _subscribeToCollection(eventId, 'prepayments');
            await _subscribeToCollection(eventId, 'sales_logs');
            await _subscribeToCollection(eventId, 'set_discounts');
            await _subscribeToCollection(eventId, 'checklist_items');

            LoggerUtils.logInfo('이벤트 $eventId 구독 재시작 완료', tag: _tag);
          } catch (e) {
            LoggerUtils.logError('이벤트 $eventId 구독 재시작 실패', tag: _tag, error: e);
          }
        });
        
      } catch (e) {
        LoggerUtils.logError('이벤트 $eventId 구독 재시작 중 오류', tag: _tag, error: e);
      }
    }
    
    _activeSubscriptions.value = _activeListeners.length;
    LoggerUtils.logInfo('구독 재시작 완료 - 활성 구독 수: ${_activeSubscriptions.value}', tag: _tag);
  }
  
  /// 모든 구독 취소
  void _cancelAllSubscriptions() {
    LoggerUtils.logInfo('모든 구독 취소', tag: _tag);
    
    for (final subscription in _activeListeners.values) {
      subscription.cancel();
    }
    _activeListeners.clear();
    _activeSubscriptions.value = 0;
  }
  
  /// 이벤트 구독 해제
  Future<void> unsubscribeFromEvent(int eventId) async {
    LoggerUtils.logInfo('이벤트 $eventId 구독 해제', tag: _tag);
    
    final user = _auth.currentUser;
    if (user == null) return;
    
    final keysToRemove = _activeListeners.keys
        .where((key) => key.contains('event_$eventId'))
        .toList();
    
    for (final key in keysToRemove) {
      await _activeListeners[key]?.cancel();
      _activeListeners.remove(key);
    }
    
    // 데이터 컨트롤러 정리
    _eventDataControllers[eventId]?.close();
    _eventDataControllers.remove(eventId);
    _eventDataCache.remove(eventId);
    
    _activeSubscriptions.value = _activeListeners.length;
  }
  
  /// 이벤트 전환 시 메모리 정리 - 특정 이벤트 관련 캐시만 정리
  Future<void> clearEventMemoryCache(int eventId) async {
    try {
      LoggerUtils.logInfo('이벤트 $eventId 메모리 캐시 정리 시작', tag: _tag);
      
      // 특정 이벤트의 데이터 컨트롤러 정리
      final controller = _eventDataControllers[eventId];
      if (controller != null) {
        await controller.close();
        _eventDataControllers.remove(eventId);
      }
      
      // 특정 이벤트의 캐시 데이터 정리
      _eventDataCache.remove(eventId);
      
      // 해당 이벤트의 활성 리스너 정리
      _activeListeners.removeWhere((key, value) {
        final isEventListener = key.contains('event_$eventId');
        if (isEventListener) {
          value.cancel();
        }
        return isEventListener;
      });
      
      LoggerUtils.logInfo('이벤트 $eventId 메모리 캐시 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 메모리 캐시 정리 실패: $e', tag: _tag);
    }
  }
  
  /// 서비스 종료
  Future<void> dispose() async {
    LoggerUtils.logInfo('실시간 동기화 서비스 종료', tag: _tag);
    
    // 플래그 초기화
    _isLoggingOut = false;
    
    await _connectivitySubscription.cancel();
    
    // 인증 상태 구독 취소
    await _authStateSubscription?.cancel();
    
    // 사용자 문서 리스너 중지
    _stopUserDocumentListener();

    // 사용자 설정 구독 중지
    _stopUserSettingsSubscription();

    _cancelAllSubscriptions();
    
    // 모든 컨트롤러 닫기
    for (final controller in _eventDataControllers.values) {
      await controller.close();
    }
    _eventDataControllers.clear();
    _eventDataCache.clear();
    
    await _dataChangeController.close();
  }
  
  /// 상품 추가
  Future<void> addProduct(int eventId, Product product) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      var productData = product.toJson();

      // 이미지가 있고 로컬 경로인 경우에만 Firebase Storage에 업로드
      if (product.imagePath != null &&
          product.imagePath!.isNotEmpty &&
          ImageSyncUtils.isLocalImagePath(product.imagePath!)) {
        final uploadedImageUrl = await ImageSyncUtils.uploadProductImageWithCategoryId(
          eventId,
          product.categoryId,
          product.name,
          product.imagePath!
        );
        if (uploadedImageUrl != null && ImageSyncUtils.isNetworkImagePath(uploadedImageUrl)) {
          productData['imagePath'] = uploadedImageUrl;
          LoggerUtils.logInfo('상품 이미지 업로드 완료: ${product.name}', tag: _tag);
        } else {
          // 업로드 실패 시 이미지 경로를 null로 설정하여 로컬 경로가 서버에 저장되지 않도록 함
          productData['imagePath'] = null;
          LoggerUtils.logWarning('상품 이미지 업로드 실패, 이미지 경로를 null로 설정: ${product.name}', tag: _tag);
        }
      } else if (product.imagePath != null && ImageSyncUtils.isNetworkImagePath(product.imagePath!)) {
        // 이미 Firebase URL인 경우 그대로 사용
        productData['imagePath'] = product.imagePath!;
        LoggerUtils.logDebug('이미 업로드된 이미지 사용: ${product.name}', tag: _tag);
      }

      final productRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('products')
          .doc(product.id.toString());

      productData['updatedAt'] = FieldValue.serverTimestamp(); // 서버 타임스탬프 추가
      await productRef.set(productData, SetOptions(merge: true));
      LoggerUtils.logInfo('상품 추가 완료: ${product.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('상품 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 상품 수정
  Future<void> updateProduct(int eventId, Product product) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      var productData = product.toJson();

      // 이미지가 있고 로컬 경로인 경우에만 Firebase Storage에 업로드
      if (product.imagePath != null &&
          product.imagePath!.isNotEmpty &&
          ImageSyncUtils.isLocalImagePath(product.imagePath!)) {
        final uploadedImageUrl = await ImageSyncUtils.uploadProductImageWithCategoryId(
          eventId,
          product.categoryId,
          product.name,
          product.imagePath!
        );
        if (uploadedImageUrl != null && ImageSyncUtils.isNetworkImagePath(uploadedImageUrl)) {
          productData['imagePath'] = uploadedImageUrl;
          LoggerUtils.logInfo('상품 이미지 업로드 완료: ${product.name}', tag: _tag);
        } else {
          // 업로드 실패 시 이미지 경로를 null로 설정하여 로컬 경로가 서버에 저장되지 않도록 함
          productData['imagePath'] = null;
          LoggerUtils.logWarning('상품 이미지 업로드 실패, 이미지 경로를 null로 설정: ${product.name}', tag: _tag);
        }
      } else if (product.imagePath != null && ImageSyncUtils.isNetworkImagePath(product.imagePath!)) {
        // 이미 Firebase URL인 경우 그대로 사용
        productData['imagePath'] = product.imagePath!;
        LoggerUtils.logDebug('이미 업로드된 이미지 사용: ${product.name}', tag: _tag);
      }

      final productRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('products')
          .doc(product.id.toString());

      await productRef.update(productData);
      LoggerUtils.logInfo('상품 수정 완료: ${product.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('상품 수정 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 상품 삭제
  Future<void> deleteProduct(int eventId, int productId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final productRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('products')
          .doc(productId.toString());

      // 1. 삭제할 상품 정보 조회 (이미지 경로 확인용)
      final productDoc = await productRef.get();

      // 2. Firestore에서 상품 삭제
      await productRef.delete();

      // 3. Firebase Storage에서 이미지 삭제
      if (productDoc.exists) {
        final productData = productDoc.data();
        final imagePath = productData?['imagePath'] as String?;

        if (imagePath != null && imagePath.startsWith('http')) {
          try {
            // Firebase Storage URL에서 파일 경로 추출
            final uri = Uri.parse(imagePath);
            final pathSegments = uri.pathSegments;
            if (pathSegments.length >= 2) {
              final filePath = pathSegments.sublist(1).join('/').split('?').first;
              await FirebaseUploadUtils.deleteFile(filePath);
              LoggerUtils.logInfo('상품 이미지 삭제 완료: $filePath', tag: _tag);
            }
          } catch (e) {
            LoggerUtils.logError('상품 이미지 삭제 실패: $imagePath', error: e, tag: _tag);
            // 이미지 삭제 실패는 치명적이지 않으므로 계속 진행
          }
        }
      }

      LoggerUtils.logInfo('상품 삭제 완료: $productId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('상품 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 이벤트 추가
  Future<void> addEvent(Event event) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      var eventData = event.toFirebaseMap();

      // 이미지가 있는 경우 Firebase Storage에 업로드
      if (event.imagePath != null && event.imagePath!.isNotEmpty) {
        final uploadedImageUrl = await ImageSyncUtils.uploadEventImage(event.id!, event.imagePath!);
        if (uploadedImageUrl != null) {
          eventData['imagePath'] = uploadedImageUrl;
          LoggerUtils.logInfo('행사 이미지 업로드 완료: ${event.name}', tag: _tag);
        }
      }

      final eventRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(event.id.toString());

      eventData['updatedAt'] = FieldValue.serverTimestamp(); // 서버 타임스탬프 추가
      await eventRef.set(eventData, SetOptions(merge: true));

      // 새 행사 생성 시 모든 기존 행사의 updatedAt 업데이트 (차분 동기화 누락 방지)
      await _updateAllEventTimestamps();
      LoggerUtils.logInfo('이벤트 추가 완료: ${event.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 이벤트 수정
  Future<void> updateEvent(Event event) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      var eventData = event.toFirebaseMap();

      // 이미지가 있고 로컬 경로인 경우 Firebase Storage에 업로드
      if (event.imagePath != null &&
          event.imagePath!.isNotEmpty &&
          ImageSyncUtils.isLocalImagePath(event.imagePath!)) {
        final uploadedImageUrl = await ImageSyncUtils.uploadEventImage(event.id!, event.imagePath!);
        if (uploadedImageUrl != null) {
          eventData['imagePath'] = uploadedImageUrl;
          LoggerUtils.logInfo('행사 이미지 업로드 완료: ${event.name}', tag: _tag);
        }
      }

      final eventRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(event.id.toString());

      await eventRef.update(eventData);
      LoggerUtils.logInfo('이벤트 수정 완료: ${event.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 수정 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 이벤트 삭제
  Future<void> deleteEvent(int eventId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final eventRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString());

      await eventRef.delete();
      LoggerUtils.logInfo('이벤트 삭제 완료: $eventId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('이벤트 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록 추가
  Future<void> addSalesLog(int eventId, SalesLog salesLog) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final salesLogData = salesLog.toJson();

      final salesLogRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('sales_logs')
          .doc(salesLog.id.toString());

      salesLogData['updatedAt'] = FieldValue.serverTimestamp(); // 서버 타임스탬프 추가
      await salesLogRef.set(salesLogData, SetOptions(merge: true));
      LoggerUtils.logInfo('판매 기록 추가 완료: ${salesLog.productName}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매 기록 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록 수정
  Future<void> updateSalesLog(int eventId, SalesLog salesLog) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final salesLogData = salesLog.toJson();

      final salesLogRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('sales_logs')
          .doc(salesLog.id.toString());

      await salesLogRef.update(salesLogData);
      LoggerUtils.logInfo('판매 기록 수정 완료: ${salesLog.productName}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매 기록 수정 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록 삭제
  Future<void> deleteSalesLog(int eventId, int salesLogId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final salesLogRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('sales_logs')
          .doc(salesLogId.toString());

      await salesLogRef.delete();
      LoggerUtils.logInfo('판매 기록 삭제 완료: $salesLogId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매 기록 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 상품 데이터 처리 시 이미지 다운로드
  Future<Product> _processProductWithImage(int eventId, Product product) async {
    // 이미지가 네트워크 URL인 경우 다운로드
    if (product.imagePath != null &&
        product.imagePath!.isNotEmpty &&
        ImageSyncUtils.isNetworkImagePath(product.imagePath!)) {

      try {
        final localImagePath = await ImageSyncUtils.downloadProductImage(
          eventId,
          product.id!,
          product.imagePath!
        );

        if (localImagePath != null) {
          // 로컬 이미지 경로로 업데이트
          return product.copyWith(imagePath: localImagePath);
        }
      } catch (e) {
        LoggerUtils.logWarning('상품 이미지 다운로드 실패: ${product.name}', tag: _tag, error: e);
        // 다운로드 실패해도 원본 상품 데이터는 유지
      }
    }

    return product;
  }
  
  /// 판매자 추가
  Future<void> addSeller(int eventId, Seller seller) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final sellerRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('sellers')
          .doc(seller.id.toString());

      final sellerData = seller.toJson();
      sellerData['updatedAt'] = FieldValue.serverTimestamp(); // 서버 타임스탬프 추가
      await sellerRef.set(sellerData, SetOptions(merge: true));
      LoggerUtils.logInfo('판매자 추가 완료: ${seller.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매자 수정
  Future<void> updateSeller(int eventId, Seller seller) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final sellerRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('sellers')
          .doc(seller.id.toString());

      await sellerRef.update(seller.toJson());
      LoggerUtils.logInfo('판매자 수정 완료: ${seller.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 수정 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매자 삭제
  Future<void> deleteSeller(int eventId, int sellerId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final sellerRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('sellers')
          .doc(sellerId.toString());

      await sellerRef.delete();
      LoggerUtils.logInfo('판매자 삭제 완료: $sellerId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }
  
  /// 선입금 추가
  Future<void> addPrepayment(int eventId, Prepayment prepayment) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final prepaymentRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayments')
          .doc(prepayment.id.toString());

      final prepaymentData = prepayment.toJson();
      prepaymentData['updatedAt'] = FieldValue.serverTimestamp(); // 서버 타임스탬프 추가
      await prepaymentRef.set(prepaymentData, SetOptions(merge: true));
      LoggerUtils.logInfo('선입금 추가 완료: ${prepayment.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 수정
  Future<void> updatePrepayment(int eventId, Prepayment prepayment) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final prepaymentRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayments')
          .doc(prepayment.id.toString());

      await prepaymentRef.update(prepayment.toJson());
      LoggerUtils.logInfo('선입금 수정 완료: ${prepayment.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 수정 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 삭제
  Future<void> deletePrepayment(int eventId, int prepaymentId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final prepaymentRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayments')
          .doc(prepaymentId.toString());

      await prepaymentRef.delete();
      LoggerUtils.logInfo('선입금 삭제 완료: $prepaymentId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  // ============================================================================
  // 선입금 가상 상품 실시간 동기화 메서드들
  // ============================================================================

  /// 선입금 가상 상품 추가
  Future<void> addPrepaymentVirtualProduct(int eventId, PrepaymentVirtualProduct virtualProduct) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final virtualProductRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayment_virtual_products')
          .doc(virtualProduct.id.toString());

      final virtualProductData = virtualProduct.toMap();
      virtualProductData['updatedAt'] = FieldValue.serverTimestamp(); // 서버 타임스탬프 추가
      await virtualProductRef.set(virtualProductData, SetOptions(merge: true));
      LoggerUtils.logInfo('선입금 가상 상품 추가 완료: ${virtualProduct.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 가상 상품 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 가상 상품 수정
  Future<void> updatePrepaymentVirtualProduct(int eventId, PrepaymentVirtualProduct virtualProduct) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final virtualProductRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayment_virtual_products')
          .doc(virtualProduct.id.toString());

      await virtualProductRef.update(virtualProduct.toMap());
      LoggerUtils.logInfo('선입금 가상 상품 수정 완료: ${virtualProduct.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 가상 상품 수정 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 가상 상품 삭제
  Future<void> deletePrepaymentVirtualProduct(int eventId, int virtualProductId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final virtualProductRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayment_virtual_products')
          .doc(virtualProductId.toString());

      await virtualProductRef.delete();
      LoggerUtils.logInfo('선입금 가상 상품 삭제 완료: $virtualProductId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 가상 상품 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 가상 상품 일괄 업서트 (Batch) - 500개 초과 시 분할 처리
  Future<void> batchUpsertPrepaymentVirtualProducts(int eventId, List<PrepaymentVirtualProduct> products) async {
    if (products.isEmpty) return;
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');
    const int chunkSize = 400; // 안전 마진 (Firestore limit 500)
    int processed = 0;
    try {
      for (int i = 0; i < products.length; i += chunkSize) {
        final chunk = products.sublist(i, i + chunkSize > products.length ? products.length : i + chunkSize);
        final batch = _firestore.batch();
        for (final vp in chunk) {
          final ref = _firestore
              .collection('users')
              .doc(user.uid)
              .collection('events')
              .doc(eventId.toString())
              .collection('prepayment_virtual_products')
              .doc(vp.id.toString());
          final data = vp.toMap();
          data['updatedAt'] = FieldValue.serverTimestamp();
          batch.set(ref, data, SetOptions(merge: true));
        }
        await batch.commit();
        processed += chunk.length;
        LoggerUtils.logInfo('가상 상품 배치 업서트 진행률: $processed/${products.length}', tag: _tag);
      }
      LoggerUtils.logInfo('가상 상품 배치 업서트 완료: ${products.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('가상 상품 배치 업서트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 가상 상품 일괄 삭제 (Batch) - 500개 초과 시 분할 처리
  Future<void> batchDeletePrepaymentVirtualProducts(int eventId, List<int> virtualProductIds) async {
    if (virtualProductIds.isEmpty) return;
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');
    const int chunkSize = 450; // 약간 여유
    int processed = 0;
    try {
      for (int i = 0; i < virtualProductIds.length; i += chunkSize) {
        final chunk = virtualProductIds.sublist(i, i + chunkSize > virtualProductIds.length ? virtualProductIds.length : i + chunkSize);
        final batch = _firestore.batch();
        for (final id in chunk) {
          final ref = _firestore
              .collection('users')
              .doc(user.uid)
              .collection('events')
              .doc(eventId.toString())
              .collection('prepayment_virtual_products')
              .doc(id.toString());
          batch.delete(ref);
        }
        await batch.commit();
        processed += chunk.length;
        LoggerUtils.logInfo('가상 상품 배치 삭제 진행률: $processed/${virtualProductIds.length}', tag: _tag);
      }
      LoggerUtils.logInfo('가상 상품 배치 삭제 완료: ${virtualProductIds.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('가상 상품 배치 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 일괄 업데이트 (구매 상품명/금액 변경 등) - 필요한 필드 전체 업서트
  Future<void> batchUpdatePrepayments(int eventId, List<Prepayment> prepayments) async {
    if (prepayments.isEmpty) return;
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');
    const int chunkSize = 400;
    try {
      for (int i = 0; i < prepayments.length; i += chunkSize) {
        final chunk = prepayments.sublist(i, i + chunkSize > prepayments.length ? prepayments.length : i + chunkSize);
        final batch = _firestore.batch();
        for (final p in chunk) {
          final ref = _firestore
              .collection('users')
              .doc(user.uid)
              .collection('events')
              .doc(eventId.toString())
              .collection('prepayments')
              .doc(p.id.toString());
          batch.set(ref, p.toJson(), SetOptions(merge: true));
        }
        await batch.commit();
      }
      LoggerUtils.logInfo('선입금 배치 업데이트 완료: ${prepayments.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 배치 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  // ============================================================================
  // 선입금-상품 연동 실시간 동기화 메서드들
  // ============================================================================

  /// 선입금-상품 연동 추가
  Future<void> addPrepaymentProductLink(int eventId, PrepaymentProductLink link) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final linkRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayment_product_links')
          .doc('${link.virtualProductId}_${link.productId}');

      final linkData = link.toMap();
      linkData['updatedAt'] = FieldValue.serverTimestamp(); // 서버 타임스탬프 추가
      await linkRef.set(linkData, SetOptions(merge: true));
      LoggerUtils.logInfo('선입금-상품 연동 추가 완료: ${link.virtualProductId} -> ${link.productId}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금-상품 연동 추가 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금-상품 연동 삭제
  Future<void> deletePrepaymentProductLink(int eventId, int virtualProductId, int productId) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('사용자가 로그인되지 않았습니다.');

    try {
      final linkRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayment_product_links')
          .doc('${virtualProductId}_$productId');

      await linkRef.delete();
      LoggerUtils.logInfo('선입금-상품 연동 삭제 완료: $virtualProductId -> $productId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금-상품 연동 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  // ============================================================================
  // 헬퍼 메서드들
  // ============================================================================

  /// 새 행사 생성 시 모든 기존 행사의 updatedAt을 업데이트하여 차분 동기화에서 누락되지 않도록 함
  Future<void> _updateAllEventTimestamps() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final eventsCollection = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events');

      final snapshot = await eventsCollection.get();
      final batch = _firestore.batch();

      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'updatedAt': FieldValue.serverTimestamp()});
      }

      await batch.commit();
      LoggerUtils.logInfo('모든 행사 타임스탬프 업데이트 완료: ${snapshot.docs.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logWarning('모든 행사 타임스탬프 업데이트 실패', tag: _tag, error: e);
      // 타임스탬프 업데이트 실패해도 메인 작업은 계속 진행
    }
  }

  // ============================================================================
  // 사용자 설정 실시간 동기화 메서드들
  // ============================================================================

  /// 사용자 설정 구독 시작
  void _startUserSettingsSubscription(String userId) {
    try {
      LoggerUtils.logInfo('사용자 설정 구독 시작: $userId', tag: _tag);

      _userSettingsSubscription = _firestore
          .collection('users')
          .doc(userId)
          .collection('settings')
          .doc('user_settings')
          .snapshots()
          .listen(
        (snapshot) {
          try {
            if (snapshot.exists && snapshot.data() != null) {
              final userSettings = UserSettingsExtensions.fromFirebaseMap(snapshot.data()!);
              LoggerUtils.logDebug('사용자 설정 변경 감지: lastWorkspaceId=${userSettings.lastWorkspaceId}, realtimeSyncEnabled=${userSettings.realtimeSyncEnabled}', tag: _tag);

              // 실시간 동기화 설정 변경 확인 및 적용
              if (_realtimeSyncEnabled != userSettings.realtimeSyncEnabled) {
                LoggerUtils.logInfo('실시간 동기화 설정 변경 감지: ${userSettings.realtimeSyncEnabled}', tag: _tag);
                setRealtimeSyncEnabled(userSettings.realtimeSyncEnabled);
              }

              _userSettingsController.add(userSettings);

              // EventWorkspaceManager에 변경 사항 알림
              _notifyWorkspaceManagerOfSettingsChange(userSettings);
            } else {
              LoggerUtils.logDebug('사용자 설정 문서가 존재하지 않음', tag: _tag);
              _userSettingsController.add(null);
            }
          } catch (e) {
            LoggerUtils.logError('사용자 설정 데이터 파싱 실패', tag: _tag, error: e);
            _userSettingsController.add(null);
          }
        },
        onError: (error) {
          LoggerUtils.logError('사용자 설정 구독 오류', tag: _tag, error: error);
          _userSettingsController.add(null);
        },
      );

      LoggerUtils.logInfo('사용자 설정 구독 시작 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('사용자 설정 구독 시작 실패', tag: _tag, error: e);
    }
  }

  /// 사용자 설정 구독 중지
  void _stopUserSettingsSubscription() {
    try {
      _userSettingsSubscription?.cancel();
      _userSettingsSubscription = null;
      LoggerUtils.logInfo('사용자 설정 구독 중지 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('사용자 설정 구독 중지 실패', tag: _tag, error: e);
    }
  }

  /// EventWorkspaceManager에 설정 변경 알림 (실시간 행사 전환 제거)
  void _notifyWorkspaceManagerOfSettingsChange(UserSettings userSettings) {
    try {
      // 실시간 행사 전환 동기화 제거
      // 이제 행사 전환은 각 디바이스에서 독립적으로 발생하며,
      // 앱 시작 시에만 마지막 선택된 행사로 진입합니다.
      LoggerUtils.logDebug('사용자 설정 변경 감지 (실시간 행사 전환 비활성화): lastWorkspaceId=${userSettings.lastWorkspaceId}', tag: _tag);

      // 다른 데이터의 실시간 동기화는 계속 유지되지만,
      // 행사 전환은 다른 디바이스에 영향을 주지 않습니다.
    } catch (e) {
      LoggerUtils.logError('EventWorkspaceManager 설정 변경 알림 실패', tag: _tag, error: e);
    }
  }

  /// 정적 리소스 정리 (앱 종료 시 호출)
  static void shutdown() {
    // RealtimeSyncService는 인스턴스 기반이므로 개별적으로 dispose 호출 필요
    LoggerUtils.logInfo('RealtimeSyncService 정리 완료', tag: _tag);
  }
}
